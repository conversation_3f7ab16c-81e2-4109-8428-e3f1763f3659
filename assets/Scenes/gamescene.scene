[{"__type__": "cc.SceneAsset", "_name": "gamescene", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "gamescene", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 11}, {"__id__": 18}, {"__id__": 72}, {"__id__": 22}], "_active": true, "_components": [], "_prefab": {"__id__": 74}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": **********, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 75}, "_id": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 6}], "_active": true, "_components": [{"__id__": 8}, {"__id__": 9}, {"__id__": 10}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 640, "y": 360.00000000000006, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": **********, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 25.363636363636363, "_fovAxis": 1, "_orthoHeight": 428.35787089467726, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 255, "b": 82, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "daf21OCa1hMm660rOlgyMop", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "target": null, "smooth": 0.15, "_id": "dd398Y4glI0IsCL7EE+psh"}, {"__type__": "cc.Node", "_name": "PlayGround", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b9i2+6HDhEJIxiOleq2vXG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1, "height": 1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a3NNfQbRJOZqwEIAj12cQp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 5.684341886080802e-14, "_bottom": 5.684341886080802e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "c5V1EV8IpMtrIvY1OE9t2u"}, {"__type__": "cc.Node", "_name": "spwanpoints", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 12}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 17}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -1042.636, "y": -690.422, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": **********, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "87GGE01slMnLBedPHLZh6l"}, {"__type__": "cc.Node", "_name": "point1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 326.096, "y": 1053.802, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": **********, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_id": "63dnypG3RK16hF1t9q0kR9"}, {"__type__": "cc.Node", "_name": "point2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 326.096, "y": 1913.802, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": **********, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_id": "dcRIIflxNE4qnaW2i9w8Rv"}, {"__type__": "cc.Node", "_name": "point3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 326.096, "y": 266.479, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": **********, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_id": "46TdvsLS5DK4SHI9GrHIRI"}, {"__type__": "cc.Node", "_name": "point4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 3014.163, "y": 266.479, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": **********, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_id": "bfgN94LANFpLf1rD2srotl"}, {"__type__": "cc.Node", "_name": "point5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 3014.163, "y": 1053.803, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": **********, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_id": "cfzDZFbRpNUqR+ng2JTBrz"}, {"__type__": "cc.Node", "_name": "point6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 3014.163, "y": 1877.465, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": **********, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_id": "ebdAsVVHlFDb38NEIHVE+H"}, {"__type__": "cc.Node", "_name": "GameManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 19}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": **********, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dcbqoNkjtBg5W8+uTDZHUD"}, {"__type__": "b67f4UjjapGSoVG2Jvvuyl3", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": null, "playGround": {"__id__": 6}, "canvas": {"__id__": 2}, "spawnPoint": {"__id__": 11}, "camera": {"__id__": 3}, "playerHealthBar": {"__id__": 20}, "enemyCountLabel": {"__id__": 32}, "pauseButton": {"__id__": 28}, "pausePanel": {"__id__": 33}, "gameOverPanel": {"__id__": 47}, "resumeButton": {"__id__": 38}, "mainMenuButton2": {"__id__": 51}, "mainMenuButton": {"__id__": 42}, "gameOverTitleLabel": {"__id__": 60}, "performanceLabel": {"__id__": 57}, "rewardLabel": {"__id__": 54}, "_id": "4dfnb654tDppfJKQ/T2vCW"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_barSprite": {"__id__": 68}, "_mode": 0, "_totalLength": 300, "_progress": 1, "_reverse": false, "_id": "cdPTl0qlhCZJPVBrUXVt4K"}, {"__type__": "cc.Node", "_name": "PlayerHp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 22}, "_children": [{"__id__": 66}], "_active": true, "_components": [{"__id__": 69}, {"__id__": 70}, {"__id__": 20}, {"__id__": 71}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -468.394, "y": 322.751, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "29K6KpmxFA070lC0pOOC99"}, {"__type__": "cc.Node", "_name": "UICanvas", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 23}, {"__id__": 21}, {"__id__": 25}, {"__id__": 30}, {"__id__": 33}, {"__id__": 47}], "_active": true, "_components": [{"__id__": 63}, {"__id__": 64}, {"__id__": 65}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 640, "y": 360, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d175nThgdJ/In481e1Y5Bq"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_components": [{"__id__": 24}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cdCJ0LPa9IVqH2w1wIeMyC"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 1, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 428.35787089467726, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1074266112, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "54ErnusEVCgqjllF7oovL5"}, {"__type__": "cc.Node", "_name": "pauseButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_components": [{"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 29}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 587.4100000000001, "y": 322.339, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e81XpANIxCE4DfGTh31jyy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "883qJvqZ9GYrYyGvLJDZoj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "bdwl3yHr9LLacA2B29qQHp"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 25}, "_id": "8dGdb0ZotEI4ipLnZj89fV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 32.58999999999989, "_top": 17.661, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "67S5Tc2U5OyIjeeEiAp8Sz"}, {"__type__": "cc.Node", "_name": "opponentCount", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_components": [{"__id__": 31}, {"__id__": 32}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -271.251, "y": 322.839, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9beIwvjk9OYr8a6zjZLA2G"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 111.123046875, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "8dlhTkFq5H45gkiaf5p+Pp"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "剩余对手：4", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "6dkE9AqrxADrOs9exyQ9Ul"}, {"__type__": "cc.Node", "_name": "PausePanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 22}, "_children": [{"__id__": 34}], "_active": false, "_components": [{"__id__": 45}, {"__id__": 46}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b3j/6Ok3FN45u9Q4MQRXDE"}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 33}, "_children": [{"__id__": 35}, {"__id__": 39}], "_active": true, "_components": [{"__id__": 43}, {"__id__": 44}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b7HmLKuoFBaJmgpHgBZoWi"}, {"__type__": "cc.Node", "_name": "resume", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 36}, {"__id__": 37}, {"__id__": 38}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -60, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8fVt6SkdxH45Vzx8TiVGFn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "84ZbUxUztDWKJbGyMkMrLV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "cc9Flkvw9CTJ7ARhr6hkOl"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 35}, "_id": "c3vmklvItNHKBV5iP6TGCB"}, {"__type__": "cc.Node", "_name": "backtomenu", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 40}, {"__id__": 41}, {"__id__": 42}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 60, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0a6xn7wRxD36MC33hyuUg3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "29nXWi/1RA2Ikh2w63CNxM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d98h78qMtHYoE+aaSgbQxa"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 39}, "_id": "01ODVR7AtLrJ6CPfe9Ep21"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 260, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5bETzY4gpCCK/LZE5aVi6m"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 20, "_paddingRight": 20, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 20, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "94P9pTwoNK7o2DQzstX73p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 664.726, "height": 414.532}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "19oeNR8nROb5Y/6rm7sIzn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "84yXSCDS9CaZeX4sEcR8Cu"}, {"__type__": "cc.Node", "_name": "gameOverPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 22}, "_children": [{"__id__": 48}, {"__id__": 52}, {"__id__": 55}, {"__id__": 58}], "_active": false, "_components": [{"__id__": 61}, {"__id__": 62}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "74veVzaCRNsodhOUddYsFb"}, {"__type__": "cc.Node", "_name": "backtomenu", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 49}, {"__id__": 50}, {"__id__": 51}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -99.2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "00OCMl3b5L6b4TTDvu8f74"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "36Gx0DmEZCjLLTRGFNTNTc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e1JLwpKzlE3rVmgvVuY1Gg"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 48}, "_id": "6491/ucXhIBpHbLAW9o4fc"}, {"__type__": "cc.Node", "_name": "rewardLable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 53}, {"__id__": 54}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -19.486, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "04HJ/2/SZNdbGecYdVg79O"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "efuDe82dNAbKuWGgdrQoby"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "获得奖励", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "8bBcTQl6BHB59R/fddyB4y"}, {"__type__": "cc.Node", "_name": "reviewLable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 56}, {"__id__": 57}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.866, "y": 37.463, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "52BYxezARAe4YPq6HAnEqt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 55}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "511PciZ31LMp8JXepOOlck"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 55}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "表现评价", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "ccCO3hIZtEfbbadjYXKqCi"}, {"__type__": "cc.Node", "_name": "GameOverLable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 59}, {"__id__": 60}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.866, "y": 115.12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "885bNcAgRJT4qQXJwtmUBi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1fg2/2WFtJL6OKTtXeVwvD"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "游戏结束", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "d9EDiQaOFArInUx+uWpn6o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 664.726, "height": 414.532}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "66muAtMjZDS5PG950tk2z5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "61a5+nRxxO14Di+XX7avn0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f6R2+eMIVHAZyVYY66jnvd"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 24}, "_alignCanvasWithScreen": true, "_id": "ccuanNWeVJ8LiMvWUHAZpq"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "424x1XMGVCtrb3MWoy0Xj9"}, {"__type__": "cc.Node", "_name": "Bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 67}, {"__id__": 68}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -150, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "debI+RYN1Bh5actGdU1xPz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 15}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "33iq0Ih/5Gx7KJNERn81PD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 102, "g": 255, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "24a704da-2867-446d-8d1a-5e920c75e09d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d4NTM0WO9NmJNrLMfB4l1a"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 15}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d1IWGPQotG5KHVrMKJXhPW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9fd900dd-221b-4f89-8f2c-fba34243c835@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "41PnhYM7lL8ow6xlU8XthD"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 21.605999999999995, "_right": 0, "_top": 29.749000000000024, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "8cQ4Cr1otAC7CDNO0FfbSz"}, {"__type__": "cc.Node", "_name": "AIController", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 73}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": **********, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9d9+jzMZ1Fa5Gun1m2L8KH"}, {"__type__": "922b02JTO5KDrBy9ZPUoXjD", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "sceneMinX": -1048.5, "sceneMaxX": 2328.5, "sceneMinY": -692.5, "sceneMaxY": 1412.5, "_id": "53F2fMtGxB/rLjJLXfnmxf"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "1563d039-d898-4d8f-9415-9f1da853b8a3", "instance": null, "targetOverrides": null}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 76}, "shadows": {"__id__": 77}, "_skybox": {"__id__": 78}, "fog": {"__id__": 79}, "octree": {"__id__": 80}, "skin": {"__id__": 81}, "lightProbeInfo": {"__id__": 82}, "postSettings": {"__id__": 83}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]