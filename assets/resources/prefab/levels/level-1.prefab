[{"__type__": "cc.Prefab", "_name": "level-1", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "level-1", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 104}, {"__id__": 106}, {"__id__": 108}, {"__id__": 110}, {"__id__": 112}, {"__id__": 114}], "_prefab": {"__id__": 116}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "cars", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 29}, {"__id__": 53}, {"__id__": 77}], "_active": true, "_components": [{"__id__": 101}], "_prefab": {"__id__": 103}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "car-1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_active": true, "_components": [{"__id__": 18}, {"__id__": 20}, {"__id__": 22}, {"__id__": 24}, {"__id__": 26}], "_prefab": {"__id__": 28}, "_lpos": {"__type__": "cc.Vec3", "x": -610.581, "y": 344.475, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 2, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 3}, "_prefab": {"__id__": 5}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 4}, "asset": {"__uuid__": "5fa4a020-ad12-4d51-9e7d-57cecdc056ca", "__expectedType__": "cc.Prefab"}, "fileId": "40tQNkNPtP4aURzO7VMMIR", "instance": {"__id__": 6}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "ecF+pvTFxDA4n5kL51Gtxr", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 7}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 14}, {"__id__": 16}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 8}, "propertyPath": ["_name"], "value": "healthBar"}, {"__type__": "cc.TargetInfo", "localID": ["40tQNkNPtP4aURzO7VMMIR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 8}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -35, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 8}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 8}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 13}, "propertyPath": ["offsetY"], "value": 50}, {"__type__": "cc.TargetInfo", "localID": ["88S4hp27BPFpL6l7nafPlR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 15}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 300, "height": 15}}, {"__type__": "cc.TargetInfo", "localID": ["27NHqliW9Fb4bDUyccWJj+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_progress"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 19}, "_contentSize": {"__type__": "cc.Size", "width": 27.8, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15W75ZvFVLX4abw/6l2/OG"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 21}, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 1, "_allowSleep": true, "_gravityScale": 0, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93j7SzUitAWKz60YV2JexH"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 23}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 1, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 27.8, "height": 60.1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aH+r+uQZEA4UF98ujueQt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 25}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49e36654-b2c3-47d9-afeb-5db28286a1b0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ad89w3uVNKW5W61hU9PuBC"}, {"__type__": "ff4775+zu9CyIBPyXgDNpx7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 27}, "maxSpeed": 20, "acceleration": 10, "brakeDeceleration": 200, "turnSpeed": 50, "friction": 1.5, "initAngle": 0, "maxHealth": 30, "healthBar": null, "destroyedSprite": {"__uuid__": "a238f344-7c25-4d83-a0ec-8c85e46d3ec8@f9941", "__expectedType__": "cc.SpriteFrame"}, "removeDelay": 3, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2qUljrOBJtYaN8r8cqVbU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4Ce+Pis1BOL5qu2B4jBnq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "car-2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 30}], "_active": true, "_components": [{"__id__": 42}, {"__id__": 44}, {"__id__": 46}, {"__id__": 48}, {"__id__": 50}], "_prefab": {"__id__": 52}, "_lpos": {"__type__": "cc.Vec3", "x": 609.901, "y": 338.207, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 2, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 29}, "_prefab": {"__id__": 31}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 30}, "asset": {"__uuid__": "5fa4a020-ad12-4d51-9e7d-57cecdc056ca", "__expectedType__": "cc.Prefab"}, "fileId": "40tQNkNPtP4aURzO7VMMIR", "instance": {"__id__": 32}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "bfdc8M3EpEkpLqR3RzQ9vT", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 33}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}, {"__id__": 38}, {"__id__": 40}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_name"], "value": "healthBar"}, {"__type__": "cc.TargetInfo", "localID": ["40tQNkNPtP4aURzO7VMMIR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 35, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 300, "height": 15}}, {"__type__": "cc.TargetInfo", "localID": ["27NHqliW9Fb4bDUyccWJj+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 41}, "propertyPath": ["_progress"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 43}, "_contentSize": {"__type__": "cc.Size", "width": 27.8, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dR5BcAr5PvZKcEL4KFpN2"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 45}, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 1, "_allowSleep": true, "_gravityScale": 0, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cftW7C8TRBLIaAIx5A6Zg3"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 47}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 1, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 27.8, "height": 60.1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45Iup+wz9N566a6jKIcsRg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 49}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49e36654-b2c3-47d9-afeb-5db28286a1b0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1Wlhu7LRIp7TbKgaKFQ6Q"}, {"__type__": "ff4775+zu9CyIBPyXgDNpx7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 51}, "maxSpeed": 20, "acceleration": 10, "brakeDeceleration": 200, "turnSpeed": 50, "friction": 1.5, "initAngle": 0, "maxHealth": 30, "healthBar": null, "destroyedSprite": {"__uuid__": "a238f344-7c25-4d83-a0ec-8c85e46d3ec8@f9941", "__expectedType__": "cc.SpriteFrame"}, "removeDelay": 3, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8yzerhoZElYGKlyNpJuZx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50sOnQ8Y1KcKSuVZQeN0qR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "car-3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 54}], "_active": true, "_components": [{"__id__": 66}, {"__id__": 68}, {"__id__": 70}, {"__id__": 72}, {"__id__": 74}], "_prefab": {"__id__": 76}, "_lpos": {"__type__": "cc.Vec3", "x": 610.325, "y": -338.674, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 2, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 53}, "_prefab": {"__id__": 55}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 54}, "asset": {"__uuid__": "5fa4a020-ad12-4d51-9e7d-57cecdc056ca", "__expectedType__": "cc.Prefab"}, "fileId": "40tQNkNPtP4aURzO7VMMIR", "instance": {"__id__": 56}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "00zwVdEFlGoLnPSiR6hmTZ", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 57}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 64}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_name"], "value": "healthBar"}, {"__type__": "cc.TargetInfo", "localID": ["40tQNkNPtP4aURzO7VMMIR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 35, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 300, "height": 15}}, {"__type__": "cc.TargetInfo", "localID": ["27NHqliW9Fb4bDUyccWJj+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_progress"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 67}, "_contentSize": {"__type__": "cc.Size", "width": 27.8, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77v2PHeqpISLHCDO/8C5P3"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 69}, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 1, "_allowSleep": true, "_gravityScale": 0, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5N+gccBdF1ItHQOpZ8uXt"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 71}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 1, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 27.8, "height": 60.1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "279Oh2IrxGZ43nUuERd9jS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 73}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49e36654-b2c3-47d9-afeb-5db28286a1b0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aeXo01Po9HQKvz3eQLwO4+"}, {"__type__": "ff4775+zu9CyIBPyXgDNpx7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 75}, "maxSpeed": 20, "acceleration": 10, "brakeDeceleration": 200, "turnSpeed": 50, "friction": 1.5, "initAngle": 0, "maxHealth": 30, "healthBar": null, "destroyedSprite": {"__uuid__": "a238f344-7c25-4d83-a0ec-8c85e46d3ec8@f9941", "__expectedType__": "cc.SpriteFrame"}, "removeDelay": 3, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87fYtMl5pLQ5Gbo91nzkrJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afi6VUYD9DcZcYwA/ar5mp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "car-4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 78}], "_active": true, "_components": [{"__id__": 90}, {"__id__": 92}, {"__id__": 94}, {"__id__": 96}, {"__id__": 98}], "_prefab": {"__id__": 100}, "_lpos": {"__type__": "cc.Vec3", "x": -610.157, "y": -338.674, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 2, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 77}, "_prefab": {"__id__": 79}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 78}, "asset": {"__uuid__": "5fa4a020-ad12-4d51-9e7d-57cecdc056ca", "__expectedType__": "cc.Prefab"}, "fileId": "40tQNkNPtP4aURzO7VMMIR", "instance": {"__id__": 80}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "398EY9QyxNvoEw6E0YVYth", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 81}, {"__id__": 83}, {"__id__": 84}, {"__id__": 85}, {"__id__": 86}, {"__id__": 88}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 82}, "propertyPath": ["_name"], "value": "healthBar"}, {"__type__": "cc.TargetInfo", "localID": ["40tQNkNPtP4aURzO7VMMIR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 82}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -35, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 82}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 82}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 300, "height": 15}}, {"__type__": "cc.TargetInfo", "localID": ["27NHqliW9Fb4bDUyccWJj+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 89}, "propertyPath": ["_progress"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 91}, "_contentSize": {"__type__": "cc.Size", "width": 27.8, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fZ46VYFJEy7Y/2MFK5FDd"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 93}, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 1, "_allowSleep": true, "_gravityScale": 0, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eRuSxKudDIpl/xUdy+cwp"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 95}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 1, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 27.8, "height": 60.1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82C4SiiX9FmZS/Pxd3gdJt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 97}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49e36654-b2c3-47d9-afeb-5db28286a1b0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eQI1JAkdCjKO5sGANMO+o"}, {"__type__": "ff4775+zu9CyIBPyXgDNpx7", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 99}, "maxSpeed": 20, "acceleration": 10, "brakeDeceleration": 200, "turnSpeed": 50, "friction": 1.5, "initAngle": 0, "maxHealth": 30, "healthBar": null, "destroyedSprite": {"__uuid__": "a238f344-7c25-4d83-a0ec-8c85e46d3ec8@f9941", "__expectedType__": "cc.SpriteFrame"}, "removeDelay": 3, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83mrF4w4ZKVo8C71aXZXMw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2It3rwSVAYonZr0sxJiGz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 102}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55QaEEFuxE8qPGg1d77psJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "54YmnXlspLyaBggn+ZgMJZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 105}, "_contentSize": {"__type__": "cc.Size", "width": 4001.783, "height": 2573.79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2b0ALzXYtJPo0X8C3Xa/1a"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 107}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0bfa81f7-9b43-4f2a-bc44-2375066c8694@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79uc5hPb9BjJZW5PetmcIM"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 109}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": -1.3, "y": 1080.4}, "_size": {"__type__": "cc.Size", "width": 3380.2, "height": 468.9}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cyAmiMBRE+6g7LbrLDhAi"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 111}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": -0.1, "y": -1206.8}, "_size": {"__type__": "cc.Size", "width": 3378, "height": 295.7}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90i9i1N2xGr7Y0i102K+Kd"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 113}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": -1746, "y": 0}, "_size": {"__type__": "cc.Size", "width": 569.6, "height": 2105.4}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38Ke9wT4JLy6aX4lrvtwau"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 115}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 1720, "y": 0.3}, "_size": {"__type__": "cc.Size", "width": 575.1, "height": 2105.4}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dccW0EEldDp6vuUIynuW0Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48dRzRqnRN2bkSv9uyUwDd", "instance": null, "targetOverrides": [{"__id__": 117}, {"__id__": 120}, {"__id__": 123}, {"__id__": 126}, {"__id__": 129}, {"__id__": 131}, {"__id__": 133}, {"__id__": 135}], "nestedPrefabInstanceRoots": [{"__id__": 78}, {"__id__": 54}, {"__id__": 30}, {"__id__": 4}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 4}, "sourceInfo": {"__id__": 118}, "propertyPath": ["_barSprite"], "target": {"__id__": 4}, "targetInfo": {"__id__": 119}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetInfo", "localID": ["e4T3FcLEZKR5ceOsPb+eF1"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 30}, "sourceInfo": {"__id__": 121}, "propertyPath": ["_barSprite"], "target": {"__id__": 30}, "targetInfo": {"__id__": 122}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetInfo", "localID": ["e4T3FcLEZKR5ceOsPb+eF1"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 54}, "sourceInfo": {"__id__": 124}, "propertyPath": ["_barSprite"], "target": {"__id__": 54}, "targetInfo": {"__id__": 125}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetInfo", "localID": ["e4T3FcLEZKR5ceOsPb+eF1"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 78}, "sourceInfo": {"__id__": 127}, "propertyPath": ["_barSprite"], "target": {"__id__": 78}, "targetInfo": {"__id__": 128}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetInfo", "localID": ["e4T3FcLEZKR5ceOsPb+eF1"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 26}, "sourceInfo": null, "propertyPath": ["healthBar"], "target": {"__id__": 4}, "targetInfo": {"__id__": 130}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 50}, "sourceInfo": null, "propertyPath": ["healthBar"], "target": {"__id__": 30}, "targetInfo": {"__id__": 132}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 74}, "sourceInfo": null, "propertyPath": ["healthBar"], "target": {"__id__": 54}, "targetInfo": {"__id__": 134}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 98}, "sourceInfo": null, "propertyPath": ["healthBar"], "target": {"__id__": 78}, "targetInfo": {"__id__": 136}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}]