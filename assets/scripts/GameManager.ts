import { _decorator, Component, Node, Prefab, instantiate, resources, UITransform, Vec<PERSON>, <PERSON><PERSON><PERSON>, director, ProgressBar, Label } from 'cc';
import { TempData } from './TempData';
import { CameraFollow } from './camera_follow';
import { player } from './player';
import { AIController } from './AIController';
import { AIPlayer } from './AIPlayer';
const { ccclass, property } = _decorator;

@ccclass('GameManager')
export class GameManager extends Component {
    @property(Node)
    playGround: Node = null!; // PlayGround节点

    @property(Node)
    canvas: Node = null!; // Canvas节点

    @property(Node)
    spawnPoint: Node = null!; // SpawnPoint节点

    @property(Node)
    camera: Node = null!; // Camera节点

    @property(ProgressBar)
    playerHealthBar: ProgressBar = null!; // 玩家血量条

    @property(Label)
    enemyCountLabel: Label = null!; // 敌人数量Label

    private static _instance: GameManager = null!;
    private aiPlayers: AIPlayer[] = [];

    private playerHP: number = 100;
    private playerMaxHP: number = 100;
    private enemyCount: number = 0;

    public static getInstance(): GameManager {
        return GameManager._instance;
    }

    onLoad() {
        if (GameManager._instance) {
            this.node.destroy();
            return;
        }
        GameManager._instance = this;
        // 设置为常驻节点
        director.addPersistRootNode(this.node);
    }

    start() {
        this.loadLevelAndCar();
    }

    loadLevelAndCar() {
        const levelId = TempData.selectedLevel;
        const carId = TempData.selectedCar;
        let mapNode: Node | null = null;
        let playerNode: Node | null = null;
        // 1. 加载并实例化场景背景
        if (levelId) {
            resources.load(`prefab/levels/${levelId}`, Prefab, (err, prefab) => {
                if (!err && prefab) {
                    mapNode = instantiate(prefab);
                    mapNode.setPosition(0, 0, 0);
                    this.playGround.addChild(mapNode);
                    
                    // 场景预制体加载完成，查找AI车辆
                    this.autoFindAIPlayers();
                    this.notifyAIControllers();
                    
                    // 2. 加载并实例化车辆
                    if (carId) {
                        resources.load(`prefab/cars/${carId}`, Prefab, (err2, prefab2) => {
                            if (!err2 && prefab2) {
                                playerNode = instantiate(prefab2);
                                // 随机选择一个SpawnPoint的子节点
                                const spawnChildren = this.spawnPoint.children;
                                if (spawnChildren.length > 0) {
                                    const randomIndex = Math.floor(Math.random() * spawnChildren.length);
                                    const spawnNode = spawnChildren[randomIndex];
                                    const spawnPos = spawnNode.getWorldPosition();
                                    // 转换为Canvas的本地坐标
                                    const localPos = this.canvas.getComponent(UITransform).convertToNodeSpaceAR(spawnPos);
                                    playerNode.setPosition(localPos);
                                    playerNode.setRotation(spawnNode.getRotation());
                                    // 设置初始角度
                                    const playerScript = playerNode.getComponent(player);
                                    if (playerScript) {
                                        playerScript.init(spawnNode.angle);
                                    }
                                    // 根据点位名称设置朝向
                                    if (["point4", "point5", "point6"].indexOf(spawnNode.name) !== -1) {
                                        console.log("生成车辆在右侧")
                                        // playerNode.setRotationFromEuler(0, 0, 90);
                                    } 
                                } 
                                this.canvas.addChild(playerNode);
                                // 3. 通知相机
                                const cameraFollow = this.camera.getComponent(CameraFollow);
                                if (cameraFollow && mapNode && playerNode) {
                                    cameraFollow.init(mapNode, playerNode);
                                }
                            }
                            if (err2) {
                                console.error('加载车辆预制体失败:', err2, carId);
                                return;
                            }
                            if (!prefab2) {
                                console.error('未找到车辆预制体:', carId);
                                return;
                            }
                        });
                    }
                }
                if (err) {
                    console.error('加载关卡预制体失败:', err, levelId);
                    return;
                }
                if (!prefab) {
                    console.error('未找到关卡预制体:', levelId);
                    return;
                }
            });
        }
    }

    /**
     * 查找所有AIPlayer组件
     */
    public autoFindAIPlayers() {
        this.aiPlayers = [];
        // 路径: Canvas → PlayGround → 场景预制体 → cars
        const scene = this.node.scene;
        if (!scene) return;
        const canvas = scene.getChildByName('Canvas');
        if (!canvas) return;
        const playGround = canvas.getChildByName('PlayGround');
        if (!playGround) return;
        const sceneNode = playGround.children[0];
        if (!sceneNode) return;
        const carsNode = sceneNode.getChildByName('cars');
        if (!carsNode) return;
        for (const carNode of carsNode.children) {
            const aiPlayer = carNode.getComponent(AIPlayer);
            if (aiPlayer) {
                this.aiPlayers.push(aiPlayer);
            }
        }
    }

    /**
     * 获取AI车辆列表
     */
    public getAIPlayers(): AIPlayer[] {
        return this.aiPlayers;
    }

    /**
     * 通知所有AIController组件场景预制体已加载完成
     */
    private notifyAIControllers() {
        const aiControllers = this.node.scene.getComponentsInChildren(AIController);
        for (const aiController of aiControllers) {
            aiController.onScenePrefabLoaded();
        }
    }

    /**
     * 减少玩家血量并刷新UI
     */
    public reducePlayerHP(amount: number) {
        console.log('减少玩家血量:', amount);
        this.playerHP = Math.max(0, this.playerHP - amount);
        this.refreshPlayerHealthBar();
    }

    /**
     * 刷新玩家血量进度条
     */
    public refreshPlayerHealthBar() {
        if (this.playerHealthBar) {
            this.playerHealthBar.progress = this.playerHP / this.playerMaxHP;
        }
    }

    /**
     * 刷新剩余敌人数量并刷新UI
     */
    public refreshEnemyCount(count: number) {
        this.enemyCount = count;
        if (this.enemyCountLabel) {
            this.enemyCountLabel.string = `敌人剩余: ${this.enemyCount}`;
        }
    }
} 