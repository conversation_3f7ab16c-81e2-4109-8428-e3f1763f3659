import { _decorator, Component, <PERSON><PERSON>, director, ToggleContainer, Toggle, Sprite, Color, Label, Node } from 'cc';
import { TempData } from './TempData';
import { PlayerManager } from './PlayerManager';
// @ts-ignore
const { ccclass, property } = _decorator;

// 车辆价格配置
interface CarPriceConfig {
    [carId: string]: number;
}

@ccclass('SelectManager')
export class SelectManager extends Component {
    @property(ToggleContainer)
    levelToggleGroup: ToggleContainer = null!;

    @property(ToggleContainer)
    carToggleGroup: ToggleContainer = null!;

    @property(Button)
    startButton: Button = null!;

    @property(Label)
    moneyLabel: Label = null!; // 显示玩家金币的标签

    @property(Label)
    insufficientMoneyLabel: Label = null!; // 金币不足提示标签

    // 车辆价格配置
    private carPrices: CarPriceConfig = {
        'car-1': 0,      // 默认车辆免费
        'car-2': 500,    // 第二辆车500金币
        'car-3': 1000,   // 第三辆车1000金币
        'car-4': 1500,   // 第四辆车1500金币
        'car-5': 2000,   // 第五辆车2000金币
    };

    private insufficientMoneyTimer: number = 0; // 金币不足提示计时器

    onLoad() {
        this.updateLevelToggles();
        this.updateCarToggles();
        this.updateMoneyDisplay();
        this.setupCarPurchaseButtons();

        // 隐藏金币不足提示
        if (this.insufficientMoneyLabel) {
            this.insufficientMoneyLabel.node.active = false;
        }
    }

    updateLevelToggles() {
        const unlockedLevels = PlayerManager.instance.playerData.unlockedLevels;
        console.log('unlockedLevels:', unlockedLevels);
        this.levelToggleGroup.toggleItems.forEach((toggle: Toggle) => {
            console.log('toggle name:', toggle.node.name);
            const isUnlocked = unlockedLevels.indexOf(toggle.node.name) !== -1;
            toggle.interactable = isUnlocked;
            const sprite = toggle.node.getComponent(Sprite);
            if (sprite) {
                sprite.color = isUnlocked ? Color.WHITE : Color.BLACK;
            }
        });
    }

    updateCarToggles() {
        const unlockedCars = PlayerManager.instance.playerData.unlockedCars;
        this.carToggleGroup.toggleItems.forEach((toggle: Toggle) => {
            const carId = toggle.node.name;
            const isUnlocked = unlockedCars.indexOf(carId) !== -1;

            // 设置车辆图标的交互性和颜色
            toggle.interactable = isUnlocked;
            const sprite = toggle.node.getComponent(Sprite);
            if (sprite) {
                sprite.color = isUnlocked ? Color.WHITE : Color.BLACK;
            }

            // 处理购买按钮的显示
            this.updateCarPurchaseButton(toggle.node, carId, isUnlocked);
        });
    }

    start() {
        if (this.startButton) {
            this.startButton.node.on(Button.EventType.CLICK, this.onStartGame, this);
        }
    }

    onStartGame() {
        // 获取当前选中的level
        const levelToggle = this.levelToggleGroup.toggleItems.find((t: any) => t.isChecked);
        // 获取当前选中的car
        const carToggle = this.carToggleGroup.toggleItems.find((t: any) => t.isChecked);

        if (!levelToggle || !carToggle) {
            // 你可以在这里弹窗提示"请选择关卡和车辆"
            return;
        }

        // 记录选择到TempData
        TempData.selectedLevel = levelToggle.node.name;
        TempData.selectedCar = carToggle.node.name;

        console.log(levelToggle.node.name,carToggle.node.name)

        // 切换到游戏场景
        director.loadScene('gamescene');
    }

    /**
     * 更新金币显示
     */
    updateMoneyDisplay() {
        if (this.moneyLabel && PlayerManager.instance) {
            const money = PlayerManager.instance.playerData.money;
            this.moneyLabel.string = `金币: ${money}`;
        }
    }

    /**
     * 设置车辆购买按钮
     */
    setupCarPurchaseButtons() {
        this.carToggleGroup.toggleItems.forEach((toggle: Toggle) => {
            const carId = toggle.node.name;
            const isUnlocked = PlayerManager.instance.playerData.unlockedCars.indexOf(carId) !== -1;
            this.updateCarPurchaseButton(toggle.node, carId, isUnlocked);
        });
    }

    /**
     * 更新单个车辆的购买按钮
     */
    updateCarPurchaseButton(carNode: Node, carId: string, isUnlocked: boolean) {
        // 查找或创建购买按钮
        let purchaseButton = carNode.getChildByName('PurchaseButton');

        if (!isUnlocked && this.carPrices[carId] !== undefined) {
            // 车辆未解锁且有价格配置，显示购买按钮
            if (!purchaseButton) {
                // 创建购买按钮（这里假设场景中已经有购买按钮节点）
                purchaseButton = carNode.getChildByName('PurchaseButton');
            }

            if (purchaseButton) {
                purchaseButton.active = true;

                // 设置按钮文本
                const buttonLabel = purchaseButton.getChildByName('Label')?.getComponent(Label);
                if (buttonLabel) {
                    buttonLabel.string = `购买 ${this.carPrices[carId]}`;
                }

                // 绑定点击事件
                const button = purchaseButton.getComponent(Button);
                if (button) {
                    button.node.off(Button.EventType.CLICK);
                    button.node.on(Button.EventType.CLICK, () => {
                        this.onPurchaseCar(carId);
                    }, this);
                }
            }
        } else {
            // 车辆已解锁或免费，隐藏购买按钮
            if (purchaseButton) {
                purchaseButton.active = false;
            }
        }
    }

    /**
     * 购买车辆
     */
    onPurchaseCar(carId: string) {
        const price = this.carPrices[carId];
        if (price === undefined) {
            console.warn(`车辆 ${carId} 没有配置价格`);
            return;
        }

        const playerManager = PlayerManager.instance;
        if (!playerManager) {
            console.error('PlayerManager 实例不存在');
            return;
        }

        // 检查玩家金币是否足够
        if (playerManager.playerData.money >= price) {
            // 扣除金币并解锁车辆
            if (playerManager.spendMoney(price)) {
                playerManager.unlockCar(carId);

                console.log(`成功购买车辆 ${carId}，花费 ${price} 金币`);

                // 更新UI显示
                this.updateCarToggles();
                this.updateMoneyDisplay();

                // 保存数据
                playerManager.savePlayerData();
            }
        } else {
            // 金币不足，显示提示
            this.showInsufficientMoneyMessage();
        }
    }

    /**
     * 显示金币不足提示
     */
    showInsufficientMoneyMessage() {
        if (this.insufficientMoneyLabel) {
            this.insufficientMoneyLabel.string = '金币不足！';
            this.insufficientMoneyLabel.node.active = true;
            this.insufficientMoneyTimer = 3.0; // 3秒后隐藏
        }
    }

    /**
     * 更新方法，处理金币不足提示的计时
     */
    update(deltaTime: number) {
        if (this.insufficientMoneyTimer > 0) {
            this.insufficientMoneyTimer -= deltaTime;
            if (this.insufficientMoneyTimer <= 0) {
                if (this.insufficientMoneyLabel) {
                    this.insufficientMoneyLabel.node.active = false;
                }
            }
        }
    }

    /**
     * 获取车辆价格
     */
    getCarPrice(carId: string): number {
        return this.carPrices[carId] || 0;
    }

    /**
     * 设置车辆价格
     */
    setCarPrice(carId: string, price: number) {
        this.carPrices[carId] = price;
    }
}